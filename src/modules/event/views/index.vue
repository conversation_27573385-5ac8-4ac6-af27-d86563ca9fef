<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="12">
				<el-button @click="router.push('/console/event/core/add')" type="primary"
					>添加核心</el-button
				>
				<el-button @click="refresh">刷新</el-button>
			</el-col>
			<el-col :span="12" style="text-align: right">
				<el-input
					placeholder="请输入标题"
					v-model="search.subject"
					style="width: 300px"
					clearable
					@keyup.enter="load"
					@clear="load"
				>
					<template #append>
						<el-button :icon="Search" @click="load" />
					</template>
				</el-input>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-tabs v-model="activeTab">
					<el-tab-pane label="当前活动" name="current">
						<el-table
							border
							style="width: 100%"
							:data="tableData"
							:max-height="tableHeight"
							:row-style="{ height: '40px' }"
							:header-cell-style="{ background: '#ebeef5', color: '#333' }"
						>
							<el-table-column fixed prop="id" label="ID" width="70" align="center">
								<template #default="scope">
									<div
										:style="{
											backgroundColor: scope.row.showColor
										}"
									>
										{{ scope.row.id }}
									</div>
								</template>
							</el-table-column>
							<el-table-column
								prop="subject"
								label="标题"
								:show-overflow-tooltip="true"
								align="left"
							>
							</el-table-column>
							<el-table-column width="150" label="学校" align="left">
								<template #default="scope">
									{{ scope.row.event_school.display_name }}
								</template>
							</el-table-column>
							<el-table-column label="发布" width="150" align="center">
								<template #default="scope">
									<el-row>
										<el-col v-if="scope.row.forum_url == 0" :span="12"
											><el-button
												@click="forumClick(scope.row, 1)"
												type="success"
												size="small"
												plain
												>论坛</el-button
											></el-col
										>
										<el-col v-else :span="12"
											><el-button
												@click="forumClick(scope.row, 2)"
												type="success"
												size="small"
												>论坛</el-button
											></el-col
										>

										<el-col v-if="scope.row.www_url == 0" :span="12"
											><el-button
												@click="wwwClick(scope.row, 1)"
												type="warning"
												size="small"
												plain
												>首页</el-button
											></el-col
										>
										<el-col v-else :span="12"
											><el-button
												@click="wwwClick(scope.row, 2)"
												type="warning"
												size="small"
												>首页</el-button
											></el-col
										>
									</el-row>
									
									<el-row>
										<el-col v-if="scope.row.apply_url == 0" :span="12"
											><el-button
												@click="applyClick(scope.row, 1)"
												type="primary"
												size="small"
												plain
												>日历</el-button
											></el-col
										>
										<el-col v-else :span="12"
											><el-button
												@click="applyClick(scope.row, 2)"
												type="primary"
												size="small"
												>日历</el-button
											></el-col
										>

										<el-col v-if="scope.row.iese_url == 0" :span="12"
											><el-button
												@click="ieseClick(scope.row, 1)"
												type="primary"
												size="small"
												plain
												>IESE</el-button
											></el-col
										>
										<el-col v-else :span="12"
											><el-button
												@click="ieseClick(scope.row, 2)"
												type="primary"
												size="small"
												>IESE</el-button
											></el-col
										>
									</el-row>
								</template>
							</el-table-column>
							<el-table-column label="推送" width="110" align="center">
								<template #default="scope">
									<el-row>
										<el-col :span="12"
											><el-button
												class="pushBtn"
												@click="pushClick(scope.row, 0)"
												type="info"
												size="small"
												:plain="scope.row.push_0 === 0"
												>0</el-button
											></el-col
										>
										<el-col :span="12"
											><el-button
												class="pushBtn"
												@click="pushClick(scope.row, 1)"
												type="primary"
												size="small"
												:plain="scope.row.push_1 === 0"
												>1</el-button
											></el-col
										>
									</el-row>
									<el-row>
										<el-col :span="12"
											><el-button
												class="pushBtn"
												@click="pushClick(scope.row, 2)"
												type="success"
												size="small"
												:plain="scope.row.push_2 === 0"
												>2</el-button
											></el-col
										>
										<el-col :span="12"
											><el-button
												class="pushBtn"
												@click="pushClick(scope.row, 3)"
												type="warning"
												size="small"
												:plain="scope.row.push_3 === 0"
												>3</el-button
											></el-col
										>
									</el-row>
								</template>
							</el-table-column>
							<el-table-column label="高亮/置顶" width="100" align="center">
								<template #default="scope">
									<el-row v-if="scope.row.forum_url != 0">
										<el-col :span="24">
											<el-dropdown trigger="click">
												<span class="el-dropdown-link">
													<el-button type="text">
														<div
															:style="{
																backgroundColor: fmColor(
																	scope.row.highlight
																),
																width: '20px',
																height: '20px',
																display: 'inline-block'
															}"
														></div>
													</el-button>
												</span>
												<template #dropdown>
													<el-dropdown-menu>
														<el-dropdown-item>
															<div class="colorWrap">
																<div
																	v-for="obj in colors"
																	:key="obj.val"
																	:style="{
																		backgroundColor: obj.color,
																		width: '30px',
																		height: '30px'
																	}"
																	:title="obj.title"
																	@click="
																		colorSel(scope.row, obj.val)
																	"
																></div>
															</div>
														</el-dropdown-item>
													</el-dropdown-menu>
												</template>
											</el-dropdown>
										</el-col>
									</el-row>
									<el-row v-if="scope.row.forum_url != 0">
										<el-col :span="24">
											<el-dropdown trigger="click">
												<span class="el-dropdown-link">{{
													fmStick(scope.row.stick)
												}}</span>
												<template #dropdown>
													<el-dropdown-menu>
														<el-dropdown-item
															v-for="obj in stick"
															:key="obj.val"
															@click="stickSel(scope.row, obj.val)"
															>{{ obj.text }}</el-dropdown-item
														>
													</el-dropdown-menu>
												</template>
											</el-dropdown>
										</el-col>
									</el-row>
								</template>
							</el-table-column>
							<el-table-column label="精华" width="80" align="center">
								<template #default="scope">
									<el-switch
										v-if="scope.row.forum_url != 0"
										v-model="scope.row.digest"
										:active-value="1"
										:inactive-value="0"
										@change="switchHandler(scope.row)"
									/>
								</template>
							</el-table-column>
							<el-table-column fixed="right" label="操作" width="100" align="center">
								<template #default="scope">
									<el-icon
										@click="externalClick(scope.row)"
										class="cursor-pointer"
										style="color: #67c23a; margin-right: 18px"
										:size="20"
										><View
									/></el-icon>

									<el-icon
										@click="editClick(scope.row)"
										class="cursor-pointer"
										style="color: #464bd7; margin-right: 8px"
										:size="20"
										><edit
									/></el-icon>

									<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row, 1)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
								</template>
							</el-table-column>
						</el-table>
					</el-tab-pane>
					<el-tab-pane label="已结束活动" name="completed">
						<el-table
							border
							style="width: 100%"
							:data="tableData2"
							:max-height="tableHeight2"
							:row-style="{ height: '40px' }"
							:header-cell-style="{ background: '#ebeef5', color: '#333' }"
						>
							<el-table-column fixed prop="id" label="ID" width="70" align="center">
							</el-table-column>
							<el-table-column
								prop="subject"
								label="标题"
								:show-overflow-tooltip="true"
								align="left"
							>
							</el-table-column>
							<el-table-column width="150" label="学校" align="left">
								<template #default="scope">
									{{ scope.row.event_school.display_name }}
								</template>
							</el-table-column>
							<el-table-column label="发布" width="100" align="center">
								<template #default="scope">
									<el-row>
										<el-col v-if="scope.row.forum_url == 0" :span="24"
											><el-button type="success" size="small" plain
												>发论坛</el-button
											></el-col
										>
										<el-col v-else :span="24"
											><el-button
												@click="forumClick(scope.row, 2)"
												type="success"
												size="small"
												>论坛帖</el-button
											></el-col
										>
									</el-row>
									<el-row>
										<el-col v-if="scope.row.www_url == 0" :span="24"
											><el-button type="warning" size="small" plain
												>发www</el-button
											></el-col
										>
										<el-col v-else :span="24"
											><el-button
												@click="wwwClick(scope.row, 2)"
												type="warning"
												size="small"
												>www帖</el-button
											></el-col
										>
									</el-row>
									<el-row>
										<el-col v-if="scope.row.apply_url == 0" :span="24"
											><el-button type="primary" size="small" plain
												>推C</el-button
											></el-col
										>
										<el-col v-else :span="24"
											><el-button
												@click="applyClick(scope.row, 2)"
												type="primary"
												size="small"
												>推C</el-button
											></el-col
										>
									</el-row>
								</template>
							</el-table-column>
							<el-table-column label="推送" width="110" align="center">
								<template #default="scope">
									<el-row>
										<el-col :span="12"
											><el-button
												class="pushBtn"
												type="info"
												size="small"
												:plain="scope.row.push_0 === 0"
												>0</el-button
											></el-col
										>
										<el-col :span="12"
											><el-button
												class="pushBtn"
												type="primary"
												size="small"
												:plain="scope.row.push_1 === 0"
												>1</el-button
											></el-col
										>
									</el-row>
									<el-row>
										<el-col :span="12"
											><el-button
												class="pushBtn"
												type="success"
												size="small"
												:plain="scope.row.push_2 === 0"
												>2</el-button
											></el-col
										>
										<el-col :span="12"
											><el-button
												class="pushBtn"
												type="warning"
												size="small"
												:plain="scope.row.push_3 === 0"
												>3</el-button
											></el-col
										>
									</el-row>
								</template>
							</el-table-column>
							<el-table-column label="高亮/置顶" width="100" align="center">
								<template #default="scope">
									<el-row v-if="scope.row.forum_url != 0">
										<el-col :span="24">
											<el-dropdown trigger="click">
												<span class="el-dropdown-link">
													<el-button type="text">
														<div
															:style="{
																backgroundColor: fmColor(
																	scope.row.highlight
																),
																width: '20px',
																height: '20px',
																display: 'inline-block'
															}"
														></div>
													</el-button>
												</span>
												<template #dropdown>
													<el-dropdown-menu>
														<el-dropdown-item>
															<div class="colorWrap">
																<div
																	v-for="obj in colors"
																	:key="obj.val"
																	:style="{
																		backgroundColor: obj.color,
																		width: '30px',
																		height: '30px'
																	}"
																	:title="obj.title"
																	@click="
																		colorSel(scope.row, obj.val)
																	"
																></div>
															</div>
														</el-dropdown-item>
													</el-dropdown-menu>
												</template>
											</el-dropdown>
										</el-col>
									</el-row>
									<el-row v-if="scope.row.forum_url != 0">
										<el-col :span="24">
											<el-dropdown trigger="click">
												<span class="el-dropdown-link">{{
													fmStick(scope.row.stick)
												}}</span>
												<template #dropdown>
													<el-dropdown-menu>
														<el-dropdown-item
															v-for="obj in stick"
															:key="obj.val"
															@click="stickSel(scope.row, obj.val)"
															>{{ obj.text }}</el-dropdown-item
														>
													</el-dropdown-menu>
												</template>
											</el-dropdown>
										</el-col>
									</el-row>
								</template>
							</el-table-column>
							<el-table-column label="精华" width="80" align="center">
								<template #default="scope">
									<el-switch
										v-if="scope.row.forum_url != 0"
										v-model="scope.row.digest"
										:active-value="1"
										:inactive-value="0"
										@change="switchHandler(scope.row)"
									/>
								</template>
							</el-table-column>
							<el-table-column fixed="right" label="操作" width="100" align="center">
								<template #default="scope">
									<el-icon
										@click="externalClick(scope.row)"
										class="cursor-pointer"
										style="color: #67c23a; margin-right: 18px"
										:size="20"
										><View
									/></el-icon>

									<el-icon
										@click="editClick(scope.row)"
										class="cursor-pointer"
										style="color: #464bd7; margin-right: 8px"
										:size="20"
										><Edit
									/></el-icon>

									<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row, 2)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
								</template>
							</el-table-column>
						</el-table>
					</el-tab-pane>
				</el-tabs>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				v-if="activeTab == 'current'"
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
			<el-pagination
				v-else
				class="pagination"
				background
				:currentPage="currentPage2"
				:page-size="pageSize2"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total2"
				@size-change="handleSizeChange2"
				@current-change="handleCurrentChange2"
			/>
		</el-row>
	</div>
	<el-dialog v-model="forumVisible" title="发布到论坛">
		<el-form :model="forumForm" label-width="100px" ref="forumFormRef" :rules="forumRules">
			<el-form-item label="活动标题" prop="subject">
				<el-input v-model="forumForm.subject" placeholder="请输入活动标题" />
				<span class="subject"
					>还可以输入 <strong>{{ remainingCharacters }}</strong> 个字符</span
				>
			</el-form-item>

			<el-form-item label="活动内容" prop="content">
				<el-input
					type="textarea"
					v-model="forumForm.content"
					placeholder="请输入活动内容"
					rows="6"
				/>
			</el-form-item>

			<el-form-item label="帖子版块">
				<el-row :gutter="20" style="width: 100%">
					<el-col :span="8">
						<el-form-item prop="fid">
							<el-select
								v-model="forumForm.fid"
								placeholder="请选择"
								@change="navChange"
							>
								<el-option-group
									v-for="group in navs"
									:key="group.fid"
									:label="group.name"
								>
									<el-option
										v-for="item in group.forums"
										:key="item.fid"
										:label="item.name"
										:value="item.fid"
									/>
								</el-option-group>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item prop="typeid">
							<el-select v-model="forumForm.typeid" placeholder="请选择">
								<el-option
									v-for="obj in subNavs"
									:key="obj.typeid"
									:label="obj.name"
									:value="obj.typeid"
								/>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form-item>
			<el-form-item label="发帖账号" prop="uid">
				<el-radio-group v-model="forumForm.uid" @change="uidChange">
					<el-radio
						v-for="obj in forumAccount"
						:key="obj.uid"
						:label="obj.username"
						:value="obj.uid"
					></el-radio>
				</el-radio-group>
			</el-form-item>
			<el-divider />
			<el-form-item>
				<div class="form-submit">
					<el-button type="primary" @click="submitForm(forumFormRef, 1)">提交</el-button>
					<el-button @click="forumVisible = false">取消</el-button>
				</div>
			</el-form-item>
		</el-form>
	</el-dialog>

	<el-dialog v-model="wwwVisible" title="发布到WWW">
		<el-form :model="wwwForm" label-width="100px" ref="wwwRef" :rules="wwwRules">
			<el-form-item label="活动标题" prop="subject">
				<el-input v-model="wwwForm.subject" placeholder="请输入活动标题" />
				<span class="subject"
					>还可以输入 <strong>{{ remainingCharacters }}</strong> 个字符</span
				>
			</el-form-item>
			<el-form-item label="活动摘要" prop="summary">
				<el-input
					type="textarea"
					v-model="wwwForm.summary"
					placeholder="请输入活动摘要"
					rows="4"
				/>
			</el-form-item>
			<el-form-item label="活动内容" prop="content">
				<el-input
					type="textarea"
					v-model="wwwForm.content"
					placeholder="请输入活动内容"
					rows="6"
				/>
			</el-form-item>
			<el-form-item label="发帖账号" prop="uid">
				<el-radio-group v-model="wwwForm.uid" @change="uidChange">
					<el-radio
						v-for="obj in wwwAccount"
						:key="obj.uid"
						:label="obj.username"
						:value="obj.uid"
					></el-radio>
				</el-radio-group>
			</el-form-item>
			<el-divider />
			<el-form-item>
				<div class="form-submit">
					<el-button type="primary" @click="submitForm(wwwRef, 2)">提交</el-button>
					<el-button @click="wwwVisible = false">取消</el-button>
				</div>
			</el-form-item>
		</el-form>
	</el-dialog>

	<el-dialog v-model="applyVisible" title="发布到申请日历">
		<el-form :model="applyForm" label-width="100px" ref="applyRef" :rules="applyRules">
			<el-form-item label="活动标题" prop="subject">
				<el-input v-model="applyForm.subject" placeholder="请输入活动标题" />
			</el-form-item>
			<el-form-item label="活动内容" prop="content">
				<el-input
					type="textarea"
					v-model="applyForm.content"
					placeholder="请输入活动内容"
					rows="6"
				/>
			</el-form-item>
			<el-form-item label="发帖账号" prop="author">
				<el-input v-model="applyForm.author" placeholder="请输入发帖账号" />
			</el-form-item>
			<el-divider />
			<el-form-item>
				<div class="form-submit">
					<el-button type="primary" @click="submitForm(applyRef, 3)">提交</el-button>
					<el-button @click="applyVisible = false">取消</el-button>
				</div>
			</el-form-item>
		</el-form>
	</el-dialog>

	<el-dialog v-model="ieseVisible" title="发布到IESE">
		<el-form :model="ieseForm" label-width="100px" ref="ieseRef" :rules="ieseRules">
			<el-form-item label="活动标题" prop="subject">
				<el-input v-model="ieseForm.subject" placeholder="请输入活动标题" />
			</el-form-item>
			<el-form-item label="活动内容" prop="content">
				<el-input
					type="textarea"
					v-model="ieseForm.content"
					placeholder="请输入活动内容"
					rows="6"
				/>
			</el-form-item>		
			
			<div v-for="(location, index) in ieseForm.locations" :key="index" class="locationWrap">
				<div class="location-header" v-if="ieseForm.locations.length > 1">
					<el-icon
						class="cursor-pointer delete-icon"
						size="20"
						color="crimson"
						@click="removeLocation(index, 1)"
						><Delete
					/></el-icon>
				</div>
				<el-form-item label="活动地区">
					<el-row>
						<el-checkbox :checked="true" disabled>{{
							location?.event_geo?.name
						}}</el-checkbox>
					</el-row>
				</el-form-item>
				<el-form-item label="活动标题" :required="true">
					<el-input v-model="location.subject" placeholder="请输入活动标题" />
				</el-form-item>
				<div style="padding-left: 125px">
					<strong style="width: 275px; display: inline-block">活动开始时间</strong>
					<strong style="width: 275px; display: inline-block">活动结束时间</strong>
				</div>
				<el-form-item label="活动时间">
					<el-date-picker
						v-model="location.event_begin_date"
						type="datetime"
						placeholder="请选择开始时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
						disabled
					/>
					<span class="split">-</span>
					<el-date-picker
						v-model="location.event_end_date"
						type="datetime"
						placeholder="请选择结束时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
						disabled
					/>
				</el-form-item>
				<div style="padding-left: 125px">
					<strong style="width: 275px; display: inline-block">推送开始时间</strong>
					<strong style="width: 275px; display: inline-block">推送结束时间</strong>
				</div>
				<el-form-item label="推送时间">
					<el-date-picker
						v-model="location.push_begin_date"
						type="datetime"
						placeholder="请选择开始时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
					<span class="split">-</span>
					<el-date-picker
						v-model="location.push_end_date"
						type="datetime"
						placeholder="请选择结束时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
				</el-form-item>
			</div>

			<el-form-item>
				<div class="form-submit">
					<el-button type="primary" @click="submitForm(ieseRef, 4)">提交</el-button>
					<el-button @click="ieseVisible = false">取消</el-button>
				</div>
			</el-form-item>
		</el-form>
	</el-dialog>

	<el-dialog v-model="push0Visible" title="推0" width="1000">
		<el-form
			:model="push0Form"
			label-width="120px"
			style="max-width: 1000px"
			ref="push0Ref"
			:rules="push0Rules"
		>
			<el-form-item label="推" :required="true">
				<el-checkbox v-model="chaseDreamChecked">ChaseDream</el-checkbox>
				<el-checkbox v-model="applyCalendarChecked">申请日历</el-checkbox>
			</el-form-item>
			<el-form-item label="机构" :required="true">
				<el-select v-model="push0Form.school_id" placeholder="请选择学校" disabled>
					<el-option
						v-for="item in organizations"
						:key="item.id"
						:label="item.display_name"
						:value="item.id"
					>
						<span style="float: left">{{ item.display_name }}</span>
						<span
							style="
								float: right;
								color: var(--el-text-color-secondary);
								font-size: 13px;
							"
						>
							{{ item.country }}
						</span>
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="WWW URL" prop="www_url">
				<el-input v-model="push0Form.www_url" placeholder="请入WWW URL" />
			</el-form-item>
			<el-form-item label="论坛 URL" prop="forum_url">
				<el-input v-model="push0Form.forum_url" placeholder="请入论坛 URL" />
			</el-form-item>
			<el-form-item label="申请日历 URL" prop="apply_url">
				<el-input v-model="push0Form.apply_url" placeholder="请入申请日历 URL" />
			</el-form-item>
			<div v-for="(location, index) in push0Form.locations" :key="index" class="locationWrap">
				<div class="location-header" v-if="push0Form.locations.length > 1">
					<el-icon
						class="cursor-pointer delete-icon"
						size="20"
						color="crimson"
						@click="removeLocation(index, 0)"
						><Delete
					/></el-icon>
				</div>
				<el-form-item label="活动地区">
					<el-row>
						<el-checkbox :checked="true" disabled>{{
							location?.event_geo?.name
						}}</el-checkbox>
					</el-row>
				</el-form-item>
				<el-form-item label="专业" :required="true">
					<el-checkbox-group v-model="location.school_major" size="small" disabled>
						<el-checkbox
							v-for="obj in school_major"
							:key="obj.id"
							:label="obj.short"
							:value="obj.id"
						/>
					</el-checkbox-group>
				</el-form-item>
				<el-form-item label="活动类型" :required="true">
					<el-checkbox-group v-model="location.event_type" size="small" disabled>
						<el-checkbox
							v-for="obj in event_type"
							:key="obj.id"
							:label="obj.name"
							:value="obj.id"
						/>
					</el-checkbox-group>
				</el-form-item>
				<el-form-item label="活动标题" :required="true">
					<el-input v-model="location.subject" placeholder="请输入活动标题" />
				</el-form-item>
				<div style="padding-left: 125px">
					<strong style="width: 275px; display: inline-block">活动开始时间</strong>
					<strong style="width: 275px; display: inline-block">活动结束时间</strong>
				</div>
				<el-form-item label="活动时间">
					<el-date-picker
						v-model="location.event_begin_date"
						type="datetime"
						placeholder="请选择开始时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
						disabled
						@change="multiDayChange(location)"
					/>
					<span class="split">-</span>
					<el-date-picker
						v-model="location.event_end_date"
						type="datetime"
						placeholder="请选择结束时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
						disabled
						@change="multiDayChange(location)"
					/>
					<el-checkbox
						v-model="location.multi_day"
						style="margin-left: 10px"
						label="跨天"
						true-value="1"
						false-value="0"
						disabled
					/>
				</el-form-item>
				<div style="padding-left: 125px">
					<strong style="width: 275px; display: inline-block">推送开始时间</strong>
					<strong style="width: 275px; display: inline-block">推送结束时间</strong>
				</div>
				<el-form-item label="推送时间">
					<el-date-picker
						v-model="location.push_begin_date"
						type="datetime"
						placeholder="请选择开始时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
					<span class="split">-</span>
					<el-date-picker
						v-model="location.push_end_date"
						type="datetime"
						placeholder="请选择结束时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
				</el-form-item>
				<el-form-item label="最新标签下线时间" :required="true">
					<el-date-picker
						v-model="location.new_flag_date"
						type="datetime"
						placeholder="请选择开始时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
				</el-form-item>
				<el-form-item label="活动排序" :required="true" size="small">
					<el-radio-group v-model="location.position" class="ml-4">
						<el-radio value="1">置顶</el-radio>
						<el-radio value="0">正常</el-radio>
						<el-radio value="-1">沉底</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="上传图片">
					<div v-if="location.image?.length > 0" class="material">
						<el-image
							style="width: 100px; height: 100px"
							:src="location.image"
							fit="cover"
						/>
						<el-icon class="del" size="20" @click="delMaterial(index)"
							><delete
						/></el-icon>
					</div>
					<el-icon
						v-else
						class="cursor-pointer"
						size="30"
						color="#CFD3DC"
						@click="addMaterial(index)"
						><CirclePlus
					/></el-icon>
					<input
						type="file"
						multiple
						accept="image/*"
						:ref="setRefs(`fileInput-${index}`)"
						style="display: none"
						@change="handleFileChange($event, index)"
					/>
				</el-form-item>
			</div>

			<el-form-item>
				<div class="form-submit">
					<el-button type="primary" @click="submitPush0Form(push0Ref)">提交</el-button>
					<el-button @click="push0Visible = false">取消</el-button>
				</div>
			</el-form-item>
		</el-form>
	</el-dialog>

	<el-dialog v-model="push1Visible" title="推1" width="1000">
		<el-form
			:model="push1Form"
			label-width="120px"
			style="max-width: 1000px"
			ref="push1Ref"
			:rules="push1Rules"
		>
			<el-form-item label="通栏" :required="true" size="small">
				<el-radio-group v-model="push1Form.html">
					<el-radio value="1">是</el-radio>
					<el-radio value="0">否</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="push1Form.html == 0" label="WWW URL" :required="true" prop="url1">
				<el-input v-model="push1Form.url1" placeholder="请入WWW URL" />
			</el-form-item>
			<el-form-item v-if="push1Form.html == 0" label="论坛 URL" :required="true" prop="url2">
				<el-input v-model="push1Form.url2" placeholder="请入论坛 URL" />
			</el-form-item>
			<div v-for="(location, index) in push1Form.locations" :key="index" class="locationWrap">
				<div class="location-header" v-if="push1Form.locations.length > 1">
					<el-icon
						class="cursor-pointer delete-icon"
						size="20"
						color="crimson"
						@click="removeLocation(index, 1)"
						><Delete
					/></el-icon>
				</div>
				<el-form-item label="活动地区">
					<el-row>
						<el-checkbox :checked="true" disabled>{{
							location?.event_geo?.name
						}}</el-checkbox>
					</el-row>
				</el-form-item>
				<el-form-item v-if="push1Form.html == 0" label="活动标题" :required="true">
					<el-input v-model="location.subject" placeholder="请输入活动标题" />
				</el-form-item>
				<el-form-item v-else label="活动内容" :required="true">
					<el-input
						v-model="location.subject"
						placeholder="请输入活动内容"
						type="textarea"
						rows="5"
					/>
				</el-form-item>
				<div style="padding-left: 125px">
					<strong style="width: 275px; display: inline-block">活动开始时间</strong>
					<strong style="width: 275px; display: inline-block">活动结束时间</strong>
				</div>
				<el-form-item label="活动时间">
					<el-date-picker
						v-model="location.event_begin_date"
						type="datetime"
						placeholder="请选择开始时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
						disabled
					/>
					<span class="split">-</span>
					<el-date-picker
						v-model="location.event_end_date"
						type="datetime"
						placeholder="请选择结束时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
						disabled
					/>
				</el-form-item>
				<div style="padding-left: 125px">
					<strong style="width: 275px; display: inline-block">推送开始时间</strong>
					<strong style="width: 275px; display: inline-block">推送结束时间</strong>
				</div>
				<el-form-item label="推送时间">
					<el-date-picker
						v-model="location.push_begin_date"
						type="datetime"
						placeholder="请选择开始时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
					<span class="split">-</span>
					<el-date-picker
						v-model="location.push_end_date"
						type="datetime"
						placeholder="请选择结束时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
				</el-form-item>
				<el-form-item label="最新标签下线时间" :required="true">
					<el-date-picker
						v-model="location.new_flag_date"
						type="datetime"
						placeholder="请选择开始时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
				</el-form-item>
			</div>

			<el-form-item>
				<div class="form-submit">
					<el-button type="primary" @click="submitPush1Form(push1Ref)">提交</el-button>
					<el-button @click="push1Visible = false">取消</el-button>
				</div>
			</el-form-item>
		</el-form>
	</el-dialog>

	<el-dialog v-model="push2Visible" title="推2" width="800">
		<el-form
			:model="push2Form"
			label-width="120px"
			style="max-width: 800px"
			ref="push2Ref"
			:rules="push2Rules"
		>
			<el-form-item label="活动标题" :required="true" prop="subject">
				<el-input v-model="push2Form.subject" placeholder="请输入活动标题" />
			</el-form-item>
			<el-form-item label="推送时间" :required="true">
				<el-form-item prop="push_begin_date">
					<el-date-picker
						v-model="push2Form.push_begin_date"
						type="datetime"
						placeholder="请选择开始时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
				</el-form-item>
				<span class="split">-</span>
				<el-form-item prop="push_end_date">
					<el-date-picker
						v-model="push2Form.push_end_date"
						type="datetime"
						placeholder="请选择结束时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
				</el-form-item>
			</el-form-item>
			<el-form-item label="WWW URL" :required="true" prop="url1">
				<el-input v-model="push2Form.url1" placeholder="请入WWW URL" />
			</el-form-item>
			<el-form-item label="上传图片" :required="true" prop="image">
				<div v-if="push2Form.image?.length > 0" class="material">
					<el-image
						style="width: 100px; height: 100px"
						:src="push2Form.image"
						fit="fit"
					/>
					<el-icon class="del" size="20" @click="delMaterial('push2')"
						><delete
					/></el-icon>
				</div>
				<el-icon
					v-else
					class="cursor-pointer"
					size="30"
					color="#CFD3DC"
					@click="addMaterial('push2')"
					><CirclePlus
				/></el-icon>
				<input
					type="file"
					multiple
					accept="image/*"
					:ref="setRefs(`fileInput-push2`)"
					style="display: none"
					@change="handleFileChange($event, 'push2')"
				/>
			</el-form-item>

			<el-form-item>
				<div class="form-submit">
					<el-button type="primary" @click="submitPush2Form(push2Ref)">提交</el-button>
					<el-button @click="push2Visible = false">取消</el-button>
				</div>
			</el-form-item>
		</el-form>
	</el-dialog>

	<el-dialog v-model="push3Visible" title="推3" width="800">
		<el-form
			:model="push3Form"
			label-width="120px"
			style="max-width: 800px"
			ref="push3Ref"
			:rules="push3Rules"
		>
			<el-form-item label="活动标题" :required="true" prop="subject">
				<el-input v-model="push3Form.subject" placeholder="请输入活动标题" />
			</el-form-item>
			<el-form-item label="推送时间" :required="true">
				<el-form-item prop="push_begin_date">
					<el-date-picker
						v-model="push3Form.push_begin_date"
						type="datetime"
						placeholder="请选择开始时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
				</el-form-item>
				<span class="split">-</span>
				<el-form-item prop="push_end_date">
					<el-date-picker
						v-model="push3Form.push_end_date"
						type="datetime"
						placeholder="请选择结束时间"
						format="YYYY-MM-DD HH:mm"
						value-format="X"
					/>
				</el-form-item>
			</el-form-item>
			<el-form-item label="WWW URL" :required="true" prop="url1">
				<el-input v-model="push3Form.url1" placeholder="请入WWW URL" />
			</el-form-item>
			<el-form-item label="颜色" :required="true" prop="color">
				<el-button
					v-for="(color, index) in push3Color"
					:class="push3Form.color == color ? 'color' : color"
					:key="index"
					:color="color"
					size="small"
					@click="changeColor(color)"
				></el-button>
			</el-form-item>

			<el-form-item>
				<div class="form-submit">
					<el-button type="primary" @click="submitPush3Form(push3Ref)">提交</el-button>
					<el-button @click="push3Visible = false">取消</el-button>
				</div>
			</el-form-item>
		</el-form>
	</el-dialog>

	<el-dialog class="dialog" v-model="viewForm.visible" :title="viewForm.title" width="900" center>
		<el-row
			><div class="statistics">
				总展示：<span class="num cursor-pointer" @click="totalDisplayCountClick(1)"
					><strong>{{ totalDisplayCount }}</strong></span
				><el-input
					v-if="totalDisplayCountS"
					v-model="totalDisplayCount"
					class="text"
					placeholder="总展示"
					@keyup.enter="totalDisplayCountClick(2)"
				/>
				总跳转：<span class="num cursor-pointer" @click="totalRedirectCountClick(1)"
					><strong>{{ totalRedirectCount }}</strong></span
				><el-input
					v-if="totalRedirectCountS"
					v-model="totalRedirectCount"
					class="text"
					placeholder="总跳转"
					@keyup.enter="totalRedirectCountClick(2)"
				/>
				发布天数：<span class="num"
					><strong>{{ publicationDays }}</strong></span
				>天
			</div></el-row
		>
		<el-row class="list">
			<el-table
				:data="viewForm.data"
				:row-style="{ height: '40px' }"
				style="width: 100%"
				height="520"
				row-key="id"
				border
			>
				<el-table-column
					prop="targetUrl"
					label="外链"
					:show-overflow-tooltip="true"
					align="left"
				>
					<template #default="scope">
						<el-button
							link
							type="primary"
							plain
							@click="copyUrl(scope.row.targetUrl)"
							>{{ scope.row.targetUrl }}</el-button
						>
					</template>
				</el-table-column>
				<el-table-column prop="shortUrl" width="110" label="短链接" align="left">
					<template #default="scope">
						<el-button
							link
							type="primary"
							plain
							@click="
								copyUrl('https://go.chasedream.com/e/' + scope.row.shortUrl + '?c=')
							"
							>{{ scope.row.shortUrl }}</el-button
						>
					</template>
				</el-table-column>
				<el-table-column width="60" label="文章" align="center">
					<template #default="scope">
						<el-button link type="primary" plain @click="hitsClick(scope.row, 'p')">{{
							scope.row.portalNum
						}}</el-button>
					</template>
				</el-table-column>
				<el-table-column width="60" label="帖子" align="center">
					<template #default="scope">
						<el-button link type="primary" plain @click="hitsClick(scope.row, 'f')">{{
							scope.row.forumNum
						}}</el-button>
					</template>
				</el-table-column>
				<el-table-column width="60" label="微信" align="center">
					<template #default="scope">
						<el-button link type="primary" plain @click="hitsClick(scope.row, 'wx')">{{
							scope.row.wechatNum
						}}</el-button>
					</template>
				</el-table-column>
				<el-table-column width="60" label="海报" align="center">
					<template #default="scope">
						<el-button link type="primary" plain @click="hitsClick(scope.row, 'hb')">{{
							scope.row.hbNum
						}}</el-button>
					</template>
				</el-table-column>
				<el-table-column width="60" label="日历" align="center">
					<template #default="scope">
						<el-button link type="primary" plain @click="hitsClick(scope.row, 'c')">{{
							scope.row.calendarNum
						}}</el-button>
					</template>
				</el-table-column>
				<el-table-column width="60" label="IESE" align="center">
					<template #default="scope">
						<el-button link type="primary" plain @click="hitsClick(scope.row, 'iesecn')">{{
							scope.row.ieseNum
						}}</el-button>
					</template>
				</el-table-column>
				<el-table-column width="60" label="其他" align="center">
					<template #default="scope">
						<el-button
							link
							type="primary"
							plain
							@click="hitsClick(scope.row, 'other')"
							>{{ scope.row.otherNum }}</el-button
						>
					</template>
				</el-table-column>
				<el-table-column prop="created_at" width="160" label="创建时间" align="center">
					<template #default="scope">
						{{ datelineToDate(scope.row.created_at) }}
					</template>
				</el-table-column>
			</el-table>
		</el-row>
	</el-dialog>

	<el-dialog class="dialog" v-model="detailForm.visible" title="统计详情" width="1150" center>
		<!-- <el-row class="header">
			<el-button link type="success" :icon="Tickets" @click="exportExcel"
				>导出Excel</el-button
			>
		</el-row> -->
		<el-row class="list">
			<el-table
				:data="detailForm.data"
				:row-style="{ height: '20px' }"
				style="width: 100%"
				height="520"
				row-key="ID"
				border
			>
				<el-table-column prop="username" label="论坛名" width="85">
					<template #default="scope">
						<el-tooltip :content="scope.row.username" placement="top">
							<div class="ellipsis-tooltip">
								{{ scope.row.username }}
							</div>
						</el-tooltip>
					</template>
				</el-table-column>
				<el-table-column label="UserAgent">
					<template #default="scope">
						<el-tooltip :content="scope.row.useragent" placement="top">
							<div class="ellipsis-tooltip">
								{{ scope.row.useragent }}
							</div>
						</el-tooltip>
					</template>
				</el-table-column>
				<el-table-column prop="ip" label="IP" width="140">
					<template #default="scope">
						<el-tooltip :content="scope.row.ip" placement="top">
							<div class="ellipsis-tooltip">
								{{ scope.row.ip }}
							</div>
						</el-tooltip>
					</template>
				</el-table-column>
				<el-table-column label="Referrer" width="150">
					<template #default="scope">
						<el-tooltip :content="scope.row.referrer" placement="top">
							<div class="ellipsis-tooltip" width="150">
								{{ scope.row.referrer }}
							</div>
						</el-tooltip>
					</template>
				</el-table-column>
				<el-table-column prop="resolution" label="分辨率" width="100" />
				<el-table-column prop="created_at" width="160" label="创建时间" align="center">
					<template #default="scope">
						{{ datelineToDate(scope.row.created_at) }}
					</template>
				</el-table-column>
			</el-table>
		</el-row>
		<el-row class="page">
			<el-pagination
				class="pagination"
				background
				:currentPage="detailForm.currentPage"
				:page-size="detailForm.pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="detailForm.total"
				@size-change="handleSizeChange3"
				@current-change="handleCurrentChange3"
			/>
		</el-row>
	</el-dialog>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import { Edit, Delete, Search, CirclePlus, View } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick, onUnmounted, computed } from "vue";
import type { FormInstance } from "element-plus";
import { useCool } from "/@/cool";
import { removeEmptyFromObject, isValueEmpty, datelineToDate } from "/@/cool/utils";
import { colors, stick, forumAccount, wwwAccount, push3Color } from "../data/index";
import { isDev } from "/@/config";
import { useClipboard } from "@vueuse/core";
import _ from "lodash";

const { service, router, refs, setRefs } = useCool();

const { copy } = useClipboard();
const activeTab = ref("current");

const search = reactive({
	id: "",
	subject: ""
});

const tableHeight = ref(0);
const tableHeight2 = ref(0);
const tableData = ref([]);
const tableData2 = ref([]);
const currentPage = ref(1);
const currentPage2 = ref(1);
const pageSize = ref(50);
const pageSize2 = ref(50);
const total = ref(0);
const total2 = ref(0);

// push 0
const organizations = ref([]);
const event_type = ref([]);
const countries = ref([]);
const regions = ref([]);
const cities = ref([]);
const school_major = ref([]);
//
const navs = ref([]);
const subNavs = ref([]);

const forumVisible = ref(false);
const wwwVisible = ref(false);
const applyVisible = ref(false);
const ieseVisible = ref(false);

const push0Visible = ref(false);
const push1Visible = ref(false);
const push2Visible = ref(false);
const push3Visible = ref(false);

const forumFormRef = ref<FormInstance>();
const wwwRef = ref<FormInstance>();
const applyRef = ref<FormInstance>();
const ieseRef = ref<FormInstance>();

const push0Ref = ref<FormInstance>();
const push1Ref = ref<FormInstance>();
const push2Ref = ref<FormInstance>();
const push3Ref = ref<FormInstance>();

const totalDisplayCount = ref("0");
const totalDisplayCountS = ref(false);
const totalRedirectCount = ref("0");
const totalRedirectCountS = ref(false);
const publicationDays = ref("");

const viewForm = reactive({
	data: [],
	visible: false,
	title: "",
	id: 0
});

const detailForm = reactive({
	data: [],
	visible: false,
	title: "",
	daterange: "",
	filter: 0,
	createTime: "",
	currentPage: 1,
	pageSize: 100,
	total: 0,
	external_id: 0,
	type: ""
});

enum EventType {
	ChaseDream = 1 << 0,
	ApplyCalendar = 1 << 1
}

const forumForm = reactive({
	eventId: 0,
	subject: "",
	content: "",
	fid: "",
	typeid: "",
	uid: "",
	username: ""
});

const wwwForm = reactive({
	eventId: 0,
	subject: "",
	summary: "",
	content: "",
	uid: "",
	username: ""
});

const applyForm = reactive({
	eventId: 0,
	subject: "",
	content: "",
	author: ""
});

const ieseForm = reactive({
	eventId: 0,
	subject: "",
	content: "",	
	locations: [
		{
			id: 0,
			subject: "",
			event_geo: {},			
			event_begin_date: "",
			event_end_date: "",
			push_begin_date: "",
			push_end_date: "",
			new_flag_date: ""
		}
	]
});

const push0Form = reactive({
	type: EventType.ChaseDream | EventType.ApplyCalendar,
	eventId: 0,
	school_id: "",
	www_url: "",
	forum_url: "",
	apply_url: "",
	locations: [
		{
			id: 0,
			subject: "",
			event_geo: {},
			school_major: [],
			event_type: [],
			event_begin_date: "",
			event_end_date: "",
			push_begin_date: "",
			push_end_date: "",
			new_flag_date: "",
			position: "",
			multi_day: "",
			image: ""
		}
	]
});
const push1Form = reactive({
	eventId: 0,
	html: "0",
	url1: "",
	url2: "",
	subject: "",
	type: 1,
	locations: [
		{
			id: 0,
			subject: "",
			event_geo: {},
			lid: "",
			event_begin_date: "",
			event_end_date: "",
			push_begin_date: "",
			push_end_date: "",
			new_flag_date: ""
		}
	]
});
const push2Form = reactive({
	id: 0,
	eventId: 0,
	subject: "",
	event_begin_date: "",
	event_end_date: "",
	push_begin_date: "",
	push_end_date: "",
	url1: "",
	image: "",
	type: 2,
	level: 1
});
const push3Form = reactive({
	id: 0,
	eventId: 0,
	subject: "",
	event_begin_date: "",
	event_end_date: "",
	push_begin_date: "",
	push_end_date: "",
	url1: "",
	color: "",
	type: 3
});

const push0Rules = reactive({});
const push1Rules = reactive({
	url1: [{ required: true, message: "请输入WWW URL", trigger: "blur" }],
	url2: [{ required: true, message: "请输入论坛 URL", trigger: "blur" }]
});
const push2Rules = reactive({
	subject: [{ required: true, message: "请输入活动标题", trigger: "blur" }],
	push_begin_date: [{ required: true, message: "请选择开始时间", trigger: "blur" }],
	push_end_date: [{ required: true, message: "请选择结束时间", trigger: "blur" }],
	url1: [{ required: true, message: "请入WWW URL", trigger: "blur" }],
	image: [{ required: true, message: "请上传图片", trigger: "blur" }]
});
const push3Rules = reactive({
	subject: [{ required: true, message: "请输入活动标题", trigger: "blur" }],
	push_begin_date: [{ required: true, message: "请选择开始时间", trigger: "blur" }],
	push_end_date: [{ required: true, message: "请选择结束时间", trigger: "blur" }],
	url1: [{ required: true, message: "请入WWW URL", trigger: "blur" }],
	color: [{ required: true, message: "请选择颜色", trigger: "change" }]
});

const forumRules = reactive({
	subject: [
		{ required: true, message: "活动标题不能为空", trigger: "blur" },
		{
			min: 3,
			max: 120,
			validator: (rule, value, callback) => {
				if (getUtf8Length(value) > 120) {
					callback(new Error("活动标题不能超过120个字符"));
				} else {
					callback();
				}
			},
			trigger: "blur"
		}
	],
	content: [
		{ required: true, message: "活动内容不能为空", trigger: "blur" },
		{ min: 3, message: "至少3个字符", trigger: "blur" }
	],
	fid: [{ required: true, message: "请选择帖子版块", trigger: "change" }],
	uid: [{ required: true, message: "请选择发帖账号", trigger: "change" }]
});
const wwwRules = reactive({
	subject: [
		{ required: true, message: "活动标题不能为空", trigger: "blur" },
		{
			min: 3,
			max: 120,
			validator: (rule, value, callback) => {
				if (getUtf8Length(value) > 120) {
					callback(new Error("活动标题不能超过120个字符"));
				} else {
					callback();
				}
			},
			trigger: "blur"
		}
	],
	summary: [
		{ required: true, message: "活动摘要不能为空", trigger: "blur" },
		{ min: 3, message: "至少3个字符", trigger: "blur" }
	],
	content: [
		{ required: true, message: "活动内容不能为空", trigger: "blur" },
		{ min: 3, message: "至少3个字符", trigger: "blur" }
	],
	uid: [{ required: true, message: "请选择发帖账号", trigger: "change" }]
});
const applyRules = reactive({
	subject: [
		{ required: true, message: "活动标题不能为空", trigger: "blur" },
		{
			min: 3,
			max: 250,
			validator: (rule, value, callback) => {
				if (getUtf8Length(value) > 250) {
					callback(new Error("活动标题不能超过250个字符"));
				} else {
					callback();
				}
			},
			trigger: "blur"
		}
	],
	content: [
		{ required: true, message: "活动内容不能为空", trigger: "blur" },
		{ min: 3, message: "至少3个字符", trigger: "blur" }
	],
	username: [{ required: true, message: "请输入发帖账号", trigger: "blur" }]
});
const ieseRules = reactive({
	subject: [
		{ required: true, message: "活动标题不能为空", trigger: "blur" },
		{
			min: 3,
			max: 250,
			validator: (rule, value, callback) => {
				if (getUtf8Length(value) > 250) {
					callback(new Error("活动标题不能超过250个字符"));
				} else {
					callback();
				}
			},
			trigger: "blur"
		}
	],
	content: [
		{ required: true, message: "活动内容不能为空", trigger: "blur" },
		{ min: 3, message: "至少3个字符", trigger: "blur" }
	],	
});

const submitForm = async (formEl: FormInstance | undefined, type) => {
	if (!formEl) return;
	await formEl.validate((valid, fields) => {
		if (!valid) return;
		if (type === 1) {
			service.base.common.forum
				.publishToForum({
					...forumForm
				})
				.then((res) => {
					ElMessage({
						message: "已提交!",
						type: "success"
					});
					forumVisible.value = false;
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else if (type === 2) {
			service.base.common.forum
				.publishToWWW({
					...wwwForm
				})
				.then((res) => {
					ElMessage({
						message: "已提交!",
						type: "success"
					});
					wwwVisible.value = false;
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else if (type === 3) {
			service.base.common.event
				.publishToApply({
					...applyForm
				})
				.then((res) => {
					ElMessage({
						message: "已提交!",
						type: "success"
					});
					applyVisible.value = false;
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else if (type === 4) {
			service.base.common.event
				.publishToIESE({
					...ieseForm
				})
				.then((res) => {
					ElMessage({
						message: "已提交!",
						type: "success"
					});
					ieseVisible.value = false;
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		}
	});
};
const submitPush0Form = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	let valid = true;
	let fieldsError: any = [];

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		if (push0Form.type === 0) {
			valid = false;
			fieldsError.push({ field: "type", message: "请选择推的哪里" });
		}

		if (
			push0Form.type & EventType.ChaseDream &&
			(push0Form.www_url.length === 0 || push0Form.forum_url.length === 0)
		) {
			valid = false;
			fieldsError.push({ field: "type", message: "请输入WWW和论坛 URL" });
		}

		if (push0Form.type & EventType.ApplyCalendar && push0Form.apply_url.length === 0) {
			valid = false;
			fieldsError.push({ field: "type", message: "请输入申请日历 URL" });
		}

		push0Form.locations.forEach((location, index) => {
			if (!location.subject) {
				valid = false;
				fieldsError.push({ index, field: "subject", message: "请输入活动标题" });
			}
			if (location.school_major.length === 0) {
				valid = false;
				fieldsError.push({ index, field: "school_major", message: "请选择至少一个专业" });
			}
			if (location.event_type.length === 0) {
				valid = false;
				fieldsError.push({ index, field: "event_type", message: "请选择至少一种活动类型" });
			}
			if (isValueEmpty(location.new_flag_date)) {
				valid = false;
				fieldsError.push({
					index,
					field: "new_flag_date",
					message: "请选择最新标签下线时间"
				});
			}
		});

		if (!valid) {
			fieldsError.forEach((error) => {
				ElMessage.error(`${error.message}`);
			});
			return;
		}

		const org = organizations.value.find((el: any) => el.id == push0Form.school_id);
		push0Form.locations.map((row: any) => {
			row.image =
				row.image ||
				`https://static.chasedream.com/events/event-IMG/${org.directory}/logo/1024.png`;
		});
		const obj: any = tableData.value.find((el: any) => el.id == push0Form.eventId);

		if (obj.push_0 == 1) {
			service.base.common.event
				.calendarEdit({
					...push0Form
				})
				.then((res) => {
					ElMessage({
						message: "修改成功!",
						type: "success"
					});

					push0Visible.value = false;
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else {
			service.base.common.event
				.calendar({
					...push0Form
				})
				.then((res) => {
					ElMessage({
						message: "添加成功!",
						type: "success"
					});

					obj.push_0 = 1;
					push0Visible.value = false;
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		}
	});
};
const submitPush1Form = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	let valid = true;
	let fieldsError = [];

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		push1Form.locations.forEach((location, index) => {
			if (!location.subject) {
				valid = false;
				fieldsError.push({ index, field: "subject", message: "请输入活动标题" });
			}
			if (isValueEmpty(location.new_flag_date)) {
				valid = false;
				fieldsError.push({
					index,
					field: "new_flag_date",
					message: "请选择最新标签下线时间"
				});
			}
		});

		if (!valid) {
			fieldsError.forEach((error) => {
				ElMessage.error(`${error.message}`);
			});
			return;
		}

		const obj: any = tableData.value.find((el: any) => el.id == push1Form.eventId);

		if (obj.push_1 == 1) {
			service.base.common.event
				.releaseEdit({
					...push1Form
				})
				.then((res) => {
					ElMessage({
						message: "修改成功!",
						type: "success"
					});

					push1Visible.value = false;
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else {
			service.base.common.event
				.releaseCreate({
					...push1Form
				})
				.then((res) => {
					ElMessage({
						message: "添加成功!",
						type: "success"
					});

					if (push1Form.html == "0") obj.push_1 = 1;
					push1Visible.value = false;
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		}
	});
};
const submitPush2Form = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		const obj: any = tableData.value.find((el: any) => el.id == push2Form.eventId);

		push2Form.event_begin_date = push2Form.push_begin_date;
		push2Form.event_end_date = push2Form.push_end_date;

		if (obj.push_2 == 1) {
			service.base.common.event
				.releaseEdit({
					...push2Form
				})
				.then((res) => {
					ElMessage({
						message: "修改成功!",
						type: "success"
					});

					push2Visible.value = false;
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else {
			service.base.common.event
				.releaseCreate({
					...push2Form
				})
				.then((res) => {
					ElMessage({
						message: "添加成功!",
						type: "success"
					});

					obj.push_2 = 1;
					push2Visible.value = false;
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		}
	});
};
const submitPush3Form = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		const obj: any = tableData.value.find((el: any) => el.id == push3Form.eventId);

		push3Form.event_begin_date = push3Form.push_begin_date;
		push3Form.event_end_date = push3Form.push_end_date;

		if (obj.push_3 == 1) {
			service.base.common.event
				.releaseEdit({
					...push3Form
				})
				.then((res) => {
					ElMessage({
						message: "修改成功!",
						type: "success"
					});

					push3Visible.value = false;
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else {
			service.base.common.event
				.releaseCreate({
					...push3Form
				})
				.then((res) => {
					ElMessage({
						message: "添加成功!",
						type: "success"
					});

					obj.push_3 = 1;
					push3Visible.value = false;
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		}
	});
};

const chaseDreamChecked = computed({
	get: () => (push0Form.type & EventType.ChaseDream) !== 0,
	set: (value) => {
		if (value) {
			push0Form.type |= EventType.ChaseDream;
		} else {
			push0Form.type &= ~EventType.ChaseDream;
		}
	}
});

const applyCalendarChecked = computed({
	get: () => (push0Form.type & EventType.ApplyCalendar) !== 0,
	set: (value) => {
		if (value) {
			push0Form.type |= EventType.ApplyCalendar;
		} else {
			push0Form.type &= ~EventType.ApplyCalendar;
		}
	}
});

const removeLocation = (index, type) => {
	const target = type == 0 ? push0Form : push1Form;
	if (target.locations[index]?.id > 0) {
		service.base.common.event
			.pushLocationDelete({
				id: target.locations[index]?.id,
				type
			})
			.then((res) => {
				target.locations.splice(index, 1);
				ElMessage({
					message: "修改成功!",
					type: "success"
				});
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	} else {
		target.locations.splice(index, 1);
	}
};

const multiDayChange = (loc) => {
	const diff = loc.event_end_date - loc.event_begin_date;
	diff >= 24 * 60 * 60 ? (loc.multi_day = "1") : (loc.multi_day = "0");
};

const changeColor = (val) => {
	push3Form.color = val;
};

const remainingCharacters = computed(() => {
	const maxLength = 120;
	let target: any;

	if (forumVisible.value) {
		target = forumForm.subject;
	} else if (wwwVisible.value) {
		target = wwwForm.subject;
	}
	return maxLength - getUtf8Length(target);
});

const uidChange = (uid) => {
	const targetAccount = forumVisible.value ? forumAccount : wwwAccount;
	const targetForm = forumVisible.value ? forumForm : wwwForm;

	const account = targetAccount.find((obj) => obj.uid === uid);
	targetForm.username = account?.username || "";
};

const getUtf8Length = (str) => {
	const blob = new Blob([str], { type: "text/plain; charset=utf-8" });
	return blob.size;
};

const totalDisplayCountClick = (type) => {
	totalDisplayCountS.value = !totalDisplayCountS.value;

	if (type == 2) {
		service.base.common.event
			.updateTotalDisplayCount({
				id: viewForm.id,
				total_display_count: totalDisplayCount.value
			})
			.then((res) => {
				ElMessage({
					message: "已更新!",
					type: "success"
				});
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	}
};

const totalRedirectCountClick = (type) => {
	totalRedirectCountS.value = !totalRedirectCountS.value;

	if (type == 2) {
		service.base.common.event
			.updateTotalRedirectCount({
				id: viewForm.id,
				total_redirect_count: totalRedirectCount.value
			})
			.then((res) => {
				ElMessage({
					message: "已更新!",
					type: "success"
				});
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	}
};

const pushClick = (row: any, type) => {
	switch (type) {
		case 0:
			{
				push0Visible.value = true;
				push0Form.eventId = row.id;
				loadOrgs();
				loadEventTypes();
				loadEvent(row.id, type);
			}
			break;
		case 1:
			{
				push1Visible.value = true;
				push1Form.eventId = row.id;
				loadEvent(row.id, type);
			}
			break;
		case 2:
			{
				push2Visible.value = true;
				push2Form.eventId = row.id;
				loadEvent(row.id, type);
			}
			break;
		case 3:
			push3Visible.value = true;
			push3Form.eventId = row.id;
			loadEvent(row.id, type);
			break;
	}
};

const ieseClick = (row: any, type) => {
	if (type === 1) {
		ieseVisible.value = true;

		ieseForm.eventId = row.id;
		ieseForm.subject = row.subject;
		ieseForm.content = row.content;		

		loadEvent(row.id, -1);		
	} else if (type === 2) {
		window.open(`https://iesebs.cn/event/${row.iese_url}`, "_blank");
	}
};

const applyClick = (row: any, type) => {
	if (type === 1) {
		applyVisible.value = true;

		applyForm.eventId = row.id;
		applyForm.subject = row.subject;
		applyForm.content = row.content;
		applyForm.author = row.author;

		loadNav();
	} else if (type === 2) {
		window.open(`https://cal.top.mba/details/${row.apply_url}`, "_blank");
	}
};

const wwwClick = (row: any, type) => {
	if (type === 1) {
		wwwVisible.value = true;

		wwwForm.eventId = row.id;
		wwwForm.subject = row.subject;
		wwwForm.content = row.content;

		loadNav();
	} else if (type === 2) {
		window.open(`https://www.chasedream.com/article/${row.www_url}`, "_blank");
	}
};

const forumClick = (row: any, type) => {
	if (type === 1) {
		forumVisible.value = true;

		forumForm.eventId = row.id;
		forumForm.subject = row.subject;
		forumForm.content = row.content;

		loadNav();
	} else if (type === 2) {
		window.open(`https://forum.chasedream.com/thread-${row.forum_url}-1-1.html`, "_blank");
	}
};

const loadNav = async () => {
	service.base.common.forum
		.nav()
		.then((res) => {
			navs.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const navChange = () => {
	service.base.common.forum
		.subnav({
			fid: forumForm.fid
		})
		.then((res) => {
			subNavs.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const reset = () => {
	search.id = "";
	search.subject = "";

	forumForm.eventId = 0;
	forumForm.subject = "";
	forumForm.content = "";
	forumForm.fid = "";
	forumForm.typeid = "";
	forumForm.uid = "";
	forumForm.username = "";

	wwwForm.eventId = 0;
	wwwForm.subject = "";
	wwwForm.summary = "";
	wwwForm.content = "";
	wwwForm.uid = "";
	wwwForm.username = "";

	applyForm.eventId = 0;
	applyForm.subject = "";
	applyForm.content = "";
	applyForm.author = "";

	ieseForm.eventId = 0;
	ieseForm.subject = "";
	ieseForm.content = "";

	push0Form.eventId = 0;
	push0Form.school_id = "";
	push0Form.www_url = "";
	push0Form.forum_url = "";
	push0Form.apply_url = "";
	push0Form.locations.length = 0;

	push1Form.eventId = 0;
	push1Form.html = "0";
	push1Form.url1 = "";
	push1Form.url2 = "";
	push1Form.subject = "";
	push1Form.type = 1;
	push1Form.locations.length = 0;

	push2Form.id = 0;
	push2Form.eventId = 0;
	push2Form.subject = "";
	push2Form.event_begin_date = "";
	push2Form.event_end_date = "";
	push2Form.push_begin_date = "";
	push2Form.push_end_date = "";
	push2Form.url1 = "";
	push2Form.image = "";
	push2Form.type = 2;
	push2Form.level = 1;

	push3Form.id = 0;
	push3Form.eventId = 0;
	push3Form.subject = "";
	push3Form.event_begin_date = "";
	push3Form.event_end_date = "";
	push3Form.push_begin_date = "";
	push3Form.push_end_date = "";
	push3Form.url1 = "";
	push3Form.color = "";
	push3Form.type = 3;
};

const copyUrl = (url) => {
	copy(url);

	ElMessage({
		message: "已复制!",
		type: "success"
	});
};

const hitsClick = (row: any, type: string) => {
	resetDetailForm();
	detailForm.visible = true;

	detailForm.external_id = row.id;
	detailForm.type = type;
	loadExternalsLogs();
};

const externalClick = (row: any) => {
	service.base.common.event
		.externals({
			eventId: row.id
		})
		.then((res) => {
			console.log(res);
			viewForm.visible = true;
			viewForm.id = row.id;
			viewForm.title = row.subject;
			viewForm.data.length = 0;
			viewForm.data = res;

			service.base.common.event.core({ eventId: row.id }).then((res) => {
				totalDisplayCount.value = res.total_display_count;
				totalRedirectCount.value = res.total_redirect_count;

				if (res.event_location && res.event_location.length > 0) {
					const maxEndDate = Math.max(...res.event_location.map((loc) => loc.end_date));
					const createdAt = new Date(res.created_at).getTime() / 1000;

					if (maxEndDate > Date.now() / 1000) {
						publicationDays.value = Math.ceil(
							(Date.now() / 1000 - createdAt) / (24 * 60 * 60)
						).toString();
					} else {
						publicationDays.value = Math.ceil(
							(maxEndDate - createdAt) / (24 * 60 * 60)
						).toString();
					}
				}
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const editClick = (row: any) => {
	router.push(`/console/event/core/edit?eventId=${row.id}`);
};

const switchHandler = (row: any) => {
	service.base.common.forum
		.highlightDigest({
			eventId: row.id,
			digest: row.digest,
			opt: "digest"
		})
		.then((res) => {
			ElMessage({
				message: "已修改!",
				type: "success"
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const deleteHandler = (row: any, type) => {
	service.base.common.event
		.remove({
			eventId: row.id
		})
		.then((res) => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			type === 1 ? refresh() : refresh2();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const colorSel = (row: any, val: any) => {
	row.highlight = val;

	service.base.common.forum
		.highlightDigest({
			eventId: row.id,
			highlight_color: val,
			opt: "highlight"
		})
		.then((res) => {
			ElMessage({
				message: "已修改!",
				type: "success"
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const stickSel = (row: any, val: any) => {
	row.stick = val;

	service.base.common.forum
		.stick({
			eventId: row.id,
			stick: val
		})
		.then((res) => {
			ElMessage({
				message: "已修改!",
				type: "success"
			});
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const fmColor = (val) => {
	const obj = _.find(colors, { val });

	return obj?.color;
};

const fmStick = (val) => {
	const obj = _.find(stick, { val });

	return obj?.text;
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};
const handleSizeChange2 = (val: number) => {
	pageSize2.value = val;
	refresh2();
};
const handleCurrentChange2 = (val: number) => {
	currentPage2.value = val;
	refresh2();
};
const handleSizeChange3 = (val: number) => {
	detailForm.pageSize = val;
	loadExternalsLogs();
};
const handleCurrentChange3 = (val: number) => {
	detailForm.currentPage = val;
	loadExternalsLogs();
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.urlPage({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res?.list;
			total.value = res?.total;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};
const refresh2 = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.event
		.urlPage({
			...s,
			status: -1,
			page: currentPage2.value,
			pageSize: pageSize2.value
		})
		.then((res) => {
			const filtered = res?.list.filter((item) => item.stick > 0);

			for (const item of filtered) {
				if (!tableData.value.find((el: any) => el.id == item.id)) {
					tableData.value.push({
						...item,
						showColor: "#FDEFF0"
					});
				}
			}

			if (currentPage2.value == 1) {
				res?.list?.sort((a, b) => {
					return b.event_location[0].end_date - a.event_location[0].end_date;
				});
			}

			tableData2.value = res?.list;
			total2.value = res?.total;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const upload = async (files: any, typeOrIndex) => {
	for (let i = 0; i < files.length; i++) {
		const formData = new FormData();
		formData.append("files", files[i]);

		try {
			if (typeOrIndex === "push2") {
				const url = isDev
					? "http://localhost:9000/dev/admin/base/open/upload4EventPush2"
					: "https://connect.chasedream.com/api/v2/admin/base/open/upload4EventPush2";

				const response = await fetch(url, {
					method: "POST",
					body: formData
				});

				if (response.ok) {
					const res = await response.json();
					push2Form.image = res.data.image;
				} else {
					ElMessage.error("Upload failed");
				}
			} else {
				const baseUrl = isDev
					? "http://localhost:9000/dev/admin/base/open/upload4Event"
					: "https://connect.chasedream.com/api/v2/admin/base/open/upload4Event";

				const params = new URLSearchParams({
					school_id: push0Form.school_id,
					type: "0"
				});

				const url = `${baseUrl}?${params.toString()}`;

				const response = await fetch(url, {
					method: "POST",
					body: formData
				});

				if (response.ok) {
					const res = await response.json();
					push0Form.locations[typeOrIndex].image = res.data.image;
				} else {
					ElMessage.error("Upload failed");
				}
			}
		} catch (err: any) {
			ElMessage.error(err.message);
		}
	}
};

const addMaterial = (index) => {
	const el = refs[`fileInput-${index}`];
	el?.click();
};

const delMaterial = (typeOrIndex) => {
	if (typeOrIndex === "push2") {
		push2Form.image = "";
	} else {
		push0Form.locations[typeOrIndex].image = "";
	}
};

const handleFileChange = async (event: any, index) => {
	const files = event.target.files;
	if (files) {
		await upload(files, index);
	}

	event.target.value = "";
};

const loadOrgs = () => {
	service.base.common.event
		.orgs({})
		.then((res) => {
			organizations.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadSchoolMajor = (school_id) => {
	service.base.common.event
		.school_major({
			school_id
		})
		.then((res) => {
			school_major.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadEventTypes = () => {
	service.base.common.event
		.type({})
		.then((res) => {
			event_type.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadReleaseInfo = (eventId, type, core) => {
	service.base.common.event
		.release1To3({ eventId, type })
		.then((res: any) => {
			if (type === 1 && res?.ers?.length > 0) {
				push1Form.url1 = res.ers[0].url1;
				push1Form.url2 = res.ers[0].url2;
				push1Form.locations.length = 0;

				for (const obj of res.ers) {
					push1Form.html = obj.html.toString();
					const location = {
						id: obj.id,
						subject: obj.subject,
						event_geo: obj.event_geo,
						lid: obj.lid,
						event_begin_date: obj.event_begin_date,
						event_end_date: obj.event_end_date,
						push_begin_date: obj.push_begin_date,
						push_end_date: obj.push_end_date,
						new_flag_date: obj.new_flag_date
					};
					push1Form.locations.push(location);
				}

				const diffs: any = res.locations.filter(
					(bItem) => !push1Form.locations.some((aItem: any) => aItem.lid == bItem.id)
				);

				if (diffs?.length > 0) {
					ElMessageBox.confirm(
						`核心活动内容新增 ${diffs.length} 条新地址信息
是否增加到推1内容中`,
						"提示",
						{
							confirmButtonText: "添加",
							cancelButtonText: "取消",
							type: "warning"
						}
					).then(() => {
						const len = push1Form.locations?.length;

						for (let [index, diff] of diffs.entries()) {
							const obj: any = {};
							obj.subject = core.subject;
							obj.event_begin_date = diff.begin_date;
							obj.event_end_date = diff.end_date;
							obj.push_begin_date = Math.floor(Date.now() / 1000);
							obj.push_end_date = diff.begin_date + 60 * 15;
							obj.new_flag_date = Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 3;
							obj.event_geo = { id: 0, name: "N/A" };
							obj.location_id = parseInt(diff.city_id, 10) || diff.province_id;
							obj.lid = diff.id;

							push1Form.locations.push(obj);
							getGeo(parseInt(diff.city_id, 10) || diff.province_id, len + index, 1);
						}
					});
				}
			} else if (type === 2 && res.length > 0) {
				const push2 = res[0];
				push2Form.id = push2.id;
				push2Form.subject = push2.subject;
				push2Form.image = push2.image;
				push2Form.url1 = push2.url1;
				push2Form.push_begin_date = push2.push_begin_date;
				push2Form.push_end_date = push2.push_end_date;
			} else if (type === 3 && res.length > 0) {
				const push3 = res[0];
				push3Form.id = push3.id;
				push3Form.subject = push3.subject;
				push3Form.color = push3.color;
				push3Form.url1 = push3.url1;
				push3Form.push_begin_date = push3.push_begin_date;
				push3Form.push_end_date = push3.push_end_date;
			}
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadCalendarInfo = (eventId, core) => {
	service.base.common.event
		.calendarInfo({ eventId })
		.then((res: any) => {
			if (res.ecs.length === 0) return;

			push0Form.www_url = res.ecs[0].url1;
			push0Form.forum_url = res.ecs[0].url2;
			push0Form.apply_url = res.ecs[0].url3;
			push0Form.locations.length = 0;
			push0Form.type = res.ecs[0].type as EventType;

			for (const ec of res.ecs) {
				const multi_day =
					ec.event_end_date - ec.event_begin_date >= 60 * 60 * 24 ? "1" : "0";

				push0Form.locations.push({
					id: ec.id,
					subject: ec.subject,
					lid: ec.lid,
					event_geo: ec.event_geo,
					school_major: ec.school_major.map((row) => row.school_major_id),
					event_type: ec.event_type.map((row) => row.event_type_id),
					event_begin_date: ec.event_begin_date,
					event_end_date: ec.event_end_date,
					push_begin_date: ec.push_begin_date,
					push_end_date: ec.push_end_date,
					new_flag_date: ec.new_flag_date,
					position: ec.position.toString(),
					multi_day,
					image: ec.image
				});
			}

			const diffs: any = res.locations.filter(
				(bItem) => !push0Form.locations.some((aItem: any) => aItem.lid == bItem.id)
			);
			if (diffs?.length > 0) {
				ElMessageBox.confirm(
					`核心活动内容新增 ${diffs.length} 条新地址信息
是否增加到推0内容中`,
					"提示",
					{
						confirmButtonText: "添加",
						cancelButtonText: "取消",
						type: "warning"
					}
				).then(() => {
					const len = push0Form.locations?.length;
					for (let [index, diff] of diffs.entries()) {
						const obj: any = {};
						obj.subject = core.subject;
						obj.event_begin_date = diff.begin_date;
						obj.event_end_date = diff.end_date;
						obj.push_begin_date = Math.floor(Date.now() / 1000);
						obj.push_end_date = diff.begin_date + 60 * 15;
						obj.new_flag_date = Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 3;
						obj.position = diff.position || "0";
						obj.school_major = diff.event_school_major;
						obj.event_type = diff.event_type;
						obj.event_geo = { id: 0, name: "N/A" };
						obj.location_id = parseInt(diff.city_id, 10) || diff.province_id;
						obj.lid = diff.id;
						obj.school_id = res.ecs[0].school_id;

						push0Form.locations.push(obj);
						getGeo(parseInt(diff.city_id, 10) || diff.province_id, len + index, 0);
					}
				});
			}
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadEvent = (eventId, type) => {
	service.base.common.event
		.core({ eventId })
		.then((res) => {
			if(type === -1){
				for (const location of res.event_location) {
					location.subject = res.subject;
					location.event_begin_date = location.begin_date;
					location.event_end_date = location.end_date;
					location.push_begin_date = Math.floor(Date.now() / 1000);
					location.push_end_date = location.begin_date + 60 * 15;
					location.new_flag_date = Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 3;
					location.position = location.position || "0";
				}
				ieseForm.locations = res.event_location;
			} else if (type === 0) {
				push0Form.school_id = res.school_id.toString();
				push0Form.www_url =
					res.www_url != 0
						? `https://www.chasedream.com/show.aspx?id=${res.www_url}&cid=11`
						: "";
				push0Form.forum_url =
					res.forum_url != 0
						? `https://forum.chasedream.com/thread-${res.forum_url}-1-1.html`
						: "";
				push0Form.apply_url =
					res.apply_url != 0 ? `https://cal.top.mba/details/${res.apply_url}` : "";
				loadSchoolMajor(push0Form.school_id);

				for (const location of res.event_location) {
					location.subject = res.subject;
					location.event_begin_date = location.begin_date;
					location.event_end_date = location.end_date;
					location.push_begin_date = Math.floor(Date.now() / 1000);
					location.push_end_date = location.begin_date + 60 * 15;
					location.new_flag_date = Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 3;
					location.position = location.position || "0";
				}
				push0Form.locations = res.event_location;

				loadCalendarInfo(eventId, res);
			} else if (type === 1) {
				for (const location of res.event_location) {
					push1Form.url1 =
						res.url1 != 0
							? `https://www.chasedream.com/show.aspx?id=${res.www_url}&cid=11`
							: "";
					push1Form.url2 =
						res.url2 != 0
							? `https://forum.chasedream.com/thread-${res.forum_url}-1-1.html`
							: "";
					location.lid = parseInt(location?.id);
					location.subject = res.subject;
					location.event_begin_date = location.begin_date;
					location.event_end_date = location.end_date;
					location.push_begin_date = Math.floor(Date.now() / 1000);
					location.push_end_date = location.begin_date + 60 * 15;
					location.new_flag_date = Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 3;
					location.position = location.position || "0";
				}
				push1Form.locations = res.event_location;
				loadReleaseInfo(eventId, type, res);
			} else if (type === 2) {
				push2Form.url1 =
					res.url1 != 0
						? `https://www.chasedream.com/show.aspx?id=${res.www_url}&cid=11`
						: "";
				push2Form.subject = res.subject;
				push2Form.event_begin_date = res.event_location[0].begin_date;
				push2Form.event_end_date = res.event_location[0].end_date;
				push2Form.push_begin_date = Math.floor(Date.now() / 1000);
				push2Form.push_end_date = res.event_location[0].begin_date;
				push2Form.subject = res.subject;

				loadReleaseInfo(eventId, type, res);
			} else if (type === 3) {
				push3Form.url1 =
					res.url1 != 0
						? `https://www.chasedream.com/show.aspx?id=${res.www_url}&cid=11`
						: "";
				push3Form.subject = res.subject;
				push3Form.event_begin_date = res.event_location[0].begin_date;
				push3Form.event_end_date = res.event_location[0].end_date;
				push3Form.push_begin_date = Math.floor(Date.now() / 1000);
				push3Form.push_end_date = res.event_location[0].begin_date;
				push3Form.subject = res.subject;

				loadReleaseInfo(eventId, type, res);
			}
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const getGeo = (id = "", index, type) => {
	service.base.common.event
		.getGeo({ id })
		.then((res) => {
			if (type === 0) {
				push0Form.locations[index].event_geo = res;
			} else if (type === 1) {
				push1Form.locations[index].event_geo = res;
			}
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const loadExternalsLogs = () => {
	service.base.common.event
		.externalsLog({
			external_id: detailForm.external_id,
			type: detailForm.type,
			page: detailForm.currentPage,
			pageSize: detailForm.pageSize
		})
		.then((res) => {
			detailForm.visible = true;

			detailForm.data.length = 0;
			detailForm.data = res[0];
			detailForm.total = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const exportExcel = (title: string, form: number) => {
	service.base.common.go
		.export({
			OriginalUrl: title,
			daterange: detailForm.daterange,
			form
		})
		.then((res) => {
			let uint8Array = new Uint8Array(res.file.data);

			const blob = new Blob([uint8Array], {
				type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
			});

			const link = document.createElement("a");
			link.href = window.URL.createObjectURL(blob);
			link.download = res.fileName;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const resetDetailForm = () => {
	detailForm.data = [];
	detailForm.visible = false;
	detailForm.title = "";
	detailForm.daterange = "";
	detailForm.filter = 0;
	detailForm.createTime = "";
	detailForm.currentPage = 1;
	detailForm.pageSize = 100;
	detailForm.total = 0;
	detailForm.external_id = 0;
	detailForm.type = "";
};

const load = () => {
	refresh();
	refresh2();
};

load();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 290;
		tableHeight2.value = window.innerHeight - 290;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 290;
			tableHeight2.value = window.innerHeight - 290;
		};
	});
});

onUnmounted(() => {
	reset();
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";
</style>
